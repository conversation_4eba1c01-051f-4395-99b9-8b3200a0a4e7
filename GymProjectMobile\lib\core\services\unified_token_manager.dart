import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:mutex/mutex.dart';

import '../constants/app_constants.dart';
import '../models/user_model.dart';
import 'api_service.dart';
import 'device_service.dart';
import 'jwt_service.dart';
import 'logging_service.dart';
import 'storage_service.dart';

/// Unified Token Manager
/// 3 senaryo için optimize edilmiş tek token yönetim sistemi
/// Race condition korumalı, performans odaklı
class UnifiedTokenManager {
  // Singleton pattern
  static final UnifiedTokenManager _instance = UnifiedTokenManager._internal();
  factory UnifiedTokenManager() => _instance;
  UnifiedTokenManager._internal();

  // Services
  final StorageService _storageService = StorageService();
  final JwtService _jwtService = JwtService();
  final ApiService _apiService = ApiService();
  final DeviceService _deviceService = DeviceService();

  // Race condition koruması için mutex
  static final Mutex _refreshMutex = Mutex();
  
  // State management
  bool _isActive = false;
  bool _isRefreshing = false;
  Timer? _foregroundTimer;
  DateTime? _lastRefreshAttempt;

  // Callbacks
  VoidCallback? _onTokenRefreshed;
  Function(String?)? _onRefreshFailed;

  /// Token manager'ı başlat
  Future<void> initialize({
    VoidCallback? onTokenRefreshed,
    Function(String?)? onRefreshFailed,
  }) async {
    try {
      LoggingService.authLog('UnifiedTokenManager initializing');
      
      _onTokenRefreshed = onTokenRefreshed;
      _onRefreshFailed = onRefreshFailed;
      _isActive = true;

      // Senaryo 1: Foreground timer'ı başlat
      await _startForegroundTokenWatch();

      LoggingService.authLog('UnifiedTokenManager initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager initialize');
    }
  }

  /// Senaryo 1: Uygulama aktifken 1 dakika kala refresh
  Future<void> _startForegroundTokenWatch() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null || !_jwtService.isTokenValid(accessToken)) {
        LoggingService.authLog('No valid token for foreground watch');
        return;
      }

      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) return;

      // 1 dakika kala refresh timer'ı kur
      final timeUntilRefresh = remainingTime - AppConstants.foregroundRefreshThreshold;

      LoggingService.authLog('Foreground token watch setup',
        details: 'Remaining: ${remainingTime.inMinutes}min, Until refresh: ${timeUntilRefresh.inMinutes}min');

      // Mevcut timer'ı durdur
      _stopForegroundTimer();

      // Eğer zaten refresh zamanı geldiyse hemen yenile
      if (timeUntilRefresh.isNegative || timeUntilRefresh.inSeconds <= 0) {
        LoggingService.authLog('Token needs immediate refresh');
        await _performTokenRefresh(TokenRefreshReason.foregroundExpiring);
        return;
      }

      // Timer'ı başlat
      _foregroundTimer = Timer(timeUntilRefresh, () async {
        LoggingService.authLog('Foreground refresh timer triggered');
        await _performTokenRefresh(TokenRefreshReason.foregroundExpiring);
      });

      LoggingService.authLog('Foreground token watch started');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager _startForegroundTokenWatch');
    }
  }

  /// Senaryo 2: App resume'da token kontrolü (1 dakikadan az kaldıysa refresh)
  Future<bool> handleAppResume() async {
    try {
      LoggingService.authLog('Handling app resume token check');

      if (!_isActive) {
        LoggingService.authLog('Token manager not active, skipping app resume check');
        return false;
      }

      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) {
        LoggingService.authLog('No access token on app resume');
        return false;
      }

      // Token geçerli mi?
      if (!_jwtService.isTokenValid(accessToken)) {
        LoggingService.authLog('Token expired on app resume, attempting refresh');
        return await _performTokenRefresh(TokenRefreshReason.appResumeExpired);
      }

      // Kalan süreyi kontrol et
      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) return false;

      LoggingService.authLog('App resume token check',
        details: 'Remaining: ${remainingTime.inMinutes}min');

      // 1 dakikadan az kaldıysa refresh et
      if (remainingTime <= AppConstants.foregroundRefreshThreshold) {
        LoggingService.authLog('Token needs refresh on app resume');
        return await _performTokenRefresh(TokenRefreshReason.appResumeExpiring);
      }

      // Token sağlıklı, foreground timer'ı yeniden başlat
      await _startForegroundTokenWatch();
      return true;

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager handleAppResume');
      return false;
    }
  }

  /// Senaryo 3: Uzun süre sonra giriş (Token süresi geçmişse logout)
  Future<bool> validateTokenOnAppStart() async {
    try {
      LoggingService.authLog('Validating token on app start');

      final accessToken = await _storageService.getAccessToken();
      final refreshToken = await _storageService.getRefreshToken();

      if (accessToken == null || refreshToken == null) {
        LoggingService.authLog('Missing tokens on app start');
        return false;
      }

      // Access token geçerli mi?
      if (_jwtService.isTokenValid(accessToken)) {
        LoggingService.authLog('Access token valid on app start');
        await _startForegroundTokenWatch();
        return true;
      }

      // Access token geçersiz, refresh token ile deneme yap
      LoggingService.authLog('Access token expired on app start, attempting refresh');
      return await _performTokenRefresh(TokenRefreshReason.appStartExpired);

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager validateTokenOnAppStart');
      return false;
    }
  }

  /// Ana token refresh metodu - Race condition korumalı
  Future<bool> _performTokenRefresh(TokenRefreshReason reason) async {
    await _refreshMutex.acquire();

    try {
      if (_isRefreshing) {
        LoggingService.authLog('Token refresh already in progress, skipping');
        return false;
      }

      _isRefreshing = true;
      LoggingService.authLog('Starting token refresh', details: 'Reason: ${reason.name}');

      // Rate limiting - son denemeden 5 saniye geçmemişse skip et
      if (_lastRefreshAttempt != null &&
          DateTime.now().difference(_lastRefreshAttempt!).inSeconds < 5) {
        LoggingService.authLog('Token refresh rate limited');
        return false;
      }

      _lastRefreshAttempt = DateTime.now();

      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        LoggingService.authLog('No refresh token available');
        _onRefreshFailed?.call(null);
        return false;
      }

      // Device info al
      DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
      if (deviceInfo == null) {
        deviceInfo = await _deviceService.getDeviceInfo();
        await _storageService.saveDeviceInfo(deviceInfo);
      }

      // Retry logic ile refresh token API çağrısı
      bool refreshSuccess = false;
      for (int attempt = 1; attempt <= AppConstants.maxTokenRefreshRetries; attempt++) {
        try {
          LoggingService.authLog('Token refresh attempt $attempt/${AppConstants.maxTokenRefreshRetries}');

          final response = await _apiService.post<Map<String, dynamic>>(
            '/auth/refresh-token',
            data: {
              'refreshToken': refreshToken,
              'deviceInfo': deviceInfo.toDeviceInfoString(),
            },
            isAuthFree: true,
          );

          if (response.statusCode == 200 && response.data?['success'] == true) {
            final data = response.data!['data'];

            // Yeni token'ları kaydet
            await _storageService.saveAccessToken(data['token']);
            await _storageService.saveRefreshToken(data['refreshToken']);

            // User data'yı güncelle
            final userModel = _jwtService.decodeToken(data['token']);
            if (userModel != null) {
              await _storageService.saveUserData(userModel);
            }

            LoggingService.authLog('Token refresh successful');
            refreshSuccess = true;
            break;
          } else {
            final errorMessage = response.data?['message'] ?? 'Token refresh failed';
            LoggingService.authLog('Token refresh attempt $attempt failed',
              details: errorMessage);

            // Cihaz revoke edilmiş mi kontrol et
            if (errorMessage.contains('DEVICE_REVOKED') ||
                errorMessage.contains('oturumunuz sonlandırılmıştır') ||
                errorMessage.contains('başka bir cihazdan giriş yapılmış')) {
              LoggingService.authLog('Device has been revoked - forcing logout');
              _onRefreshFailed?.call(errorMessage);
              return false;
            }
          }
        } catch (e) {
          LoggingService.authLog('Token refresh attempt $attempt error', details: e.toString());
        }

        // Son deneme değilse bekle
        if (attempt < AppConstants.maxTokenRefreshRetries) {
          await Future.delayed(AppConstants.tokenRefreshRetryDelay);
        }
      }

      if (refreshSuccess) {
        _onTokenRefreshed?.call();
        // Yeni token için foreground timer'ı yeniden başlat
        await _startForegroundTokenWatch();
        return true;
      } else {
        LoggingService.authLog('Token refresh failed after all attempts');
        _onRefreshFailed?.call(null);
        return false;
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager _performTokenRefresh');
      _onRefreshFailed?.call(null);
      return false;
    } finally {
      _isRefreshing = false;
      _refreshMutex.release();
    }
  }

  /// Foreground timer'ı durdur
  void _stopForegroundTimer() {
    _foregroundTimer?.cancel();
    _foregroundTimer = null;
  }

  /// Token manager'ı durdur
  void stop() {
    try {
      LoggingService.authLog('UnifiedTokenManager stopping');
      
      _isActive = false;
      _stopForegroundTimer();
      _isRefreshing = false;
      
      LoggingService.authLog('UnifiedTokenManager stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager stop');
    }
  }

  /// Token manager'ı yeniden başlat
  Future<void> restart() async {
    try {
      LoggingService.authLog('UnifiedTokenManager restarting');
      
      stop();
      await initialize(
        onTokenRefreshed: _onTokenRefreshed,
        onRefreshFailed: _onRefreshFailed,
      );
      
      LoggingService.authLog('UnifiedTokenManager restarted');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'UnifiedTokenManager restart');
    }
  }

  /// Manuel token refresh tetikle
  Future<bool> triggerManualRefresh() async {
    LoggingService.authLog('Manual token refresh triggered');
    return await _performTokenRefresh(TokenRefreshReason.manual);
  }

  /// Service durumunu kontrol et
  bool get isActive => _isActive;
  bool get isRefreshing => _isRefreshing;

  /// Service'i dispose et
  void dispose() {
    LoggingService.authLog('UnifiedTokenManager disposing');
    stop();
  }
}

/// Token refresh sebepleri
enum TokenRefreshReason {
  foregroundExpiring,
  appResumeExpiring,
  appResumeExpired,
  appStartExpired,
  manual,
}

extension TokenRefreshReasonExtension on TokenRefreshReason {
  String get name {
    switch (this) {
      case TokenRefreshReason.foregroundExpiring:
        return 'Foreground Expiring';
      case TokenRefreshReason.appResumeExpiring:
        return 'App Resume Expiring';
      case TokenRefreshReason.appResumeExpired:
        return 'App Resume Expired';
      case TokenRefreshReason.appStartExpired:
        return 'App Start Expired';
      case TokenRefreshReason.manual:
        return 'Manual';
    }
  }
}
